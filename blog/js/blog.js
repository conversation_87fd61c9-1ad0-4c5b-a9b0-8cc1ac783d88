// Blog functionality for DocForge AI
class BlogManager {
    constructor() {
        this.posts = [];
        this.filteredPosts = [];
        this.currentPage = 1;
        this.postsPerPage = 6;
        this.currentCategory = 'all';
        this.searchTerm = '';
        
        this.init();
    }

    init() {
        this.loadBlogPosts();
        this.setupEventListeners();
        this.setupMobileMenu();
    }

    // Sample blog posts data
    loadBlogPosts() {
        this.posts = [
            {
                id: 1,
                title: "Getting Started with AI-Powered Document Creation",
                excerpt: "Learn the fundamentals of using AI to create professional documents faster and more efficiently than ever before.",
                category: "ai-writing",
                date: "2025-01-15",
                author: "<PERSON>",
                readTime: "5 min read",
                image: "assets/images/ai-writing-basics.jpg",
                slug: "getting-started-with-ai-writing"
            },
            {
                id: 2,
                title: "10 Tips for Writing Better Business Documents with AI",
                excerpt: "Discover proven strategies to enhance your business writing using AI assistance while maintaining your unique voice.",
                category: "productivity",
                date: "2025-01-12",
                author: "<PERSON>",
                readTime: "7 min read",
                image: "assets/images/business-writing-tips.jpg",
                slug: "10-tips-better-business-documents"
            },
            {
                id: 3,
                title: "How to Create Professional Reports 10x Faster",
                excerpt: "Step-by-step guide to streamlining your report creation process using DocForge AI's advanced features.",
                category: "tutorials",
                date: "2025-01-10",
                author: "Amanda Taylor",
                readTime: "8 min read",
                image: "assets/images/professional-reports.jpg",
                slug: "create-professional-reports-faster"
            },
            {
                id: 4,
                title: "The Future of Document Creation: AI vs Traditional Tools",
                excerpt: "Explore how AI is revolutionizing document creation and what it means for professionals across industries.",
                category: "ai-writing",
                date: "2025-01-08",
                author: "David Kim",
                readTime: "6 min read",
                image: "assets/images/future-document-creation.jpg",
                slug: "future-of-document-creation"
            },
            {
                id: 5,
                title: "Case Study: How TechCorp Saved 20 Hours Per Week",
                excerpt: "Real-world success story showing how one company transformed their document workflow with DocForge AI.",
                category: "case-studies",
                date: "2025-01-05",
                author: "Lisa Johnson",
                readTime: "4 min read",
                image: "assets/images/techcorp-case-study.jpg",
                slug: "techcorp-case-study"
            },
            {
                id: 6,
                title: "Advanced Features: Mastering Templates and Automation",
                excerpt: "Deep dive into DocForge AI's most powerful features for creating custom templates and automating repetitive tasks.",
                category: "features",
                date: "2025-01-03",
                author: "Robert Chen",
                readTime: "9 min read",
                image: "assets/images/advanced-features.jpg",
                slug: "mastering-templates-automation"
            }
        ];

        this.filteredPosts = [...this.posts];
        this.renderPosts();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('blog-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.filterPosts();
            });
        }

        // Category filters
        const categoryFilters = document.querySelectorAll('.category-filter');
        categoryFilters.forEach(filter => {
            filter.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Update active state
                categoryFilters.forEach(f => f.classList.remove('active'));
                e.target.classList.add('active');
                
                // Filter posts
                this.currentCategory = e.target.dataset.category;
                this.currentPage = 1;
                this.filterPosts();
            });
        });

        // Load more button
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.currentPage++;
                this.renderPosts(true);
            });
        }
    }

    setupMobileMenu() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }
    }

    filterPosts() {
        this.filteredPosts = this.posts.filter(post => {
            const matchesCategory = this.currentCategory === 'all' || post.category === this.currentCategory;
            const matchesSearch = this.searchTerm === '' || 
                post.title.toLowerCase().includes(this.searchTerm) ||
                post.excerpt.toLowerCase().includes(this.searchTerm) ||
                post.author.toLowerCase().includes(this.searchTerm);
            
            return matchesCategory && matchesSearch;
        });

        this.currentPage = 1;
        this.renderPosts();
    }

    renderPosts(append = false) {
        const grid = document.getElementById('blog-posts-grid');
        const loadMoreBtn = document.getElementById('load-more-btn');
        
        if (!grid) return;

        const startIndex = append ? (this.currentPage - 1) * this.postsPerPage : 0;
        const endIndex = this.currentPage * this.postsPerPage;
        const postsToShow = this.filteredPosts.slice(startIndex, endIndex);

        if (!append) {
            grid.innerHTML = '';
        }

        postsToShow.forEach(post => {
            const postElement = this.createPostElement(post);
            grid.appendChild(postElement);
        });

        // Show/hide load more button
        if (loadMoreBtn) {
            if (endIndex >= this.filteredPosts.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.style.display = 'block';
            }
        }

        // Show no results message if needed
        if (this.filteredPosts.length === 0 && !append) {
            grid.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No articles found</h3>
                    <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
                </div>
            `;
        }
    }

    createPostElement(post) {
        const article = document.createElement('article');
        article.className = 'card hover-lift fade-in';
        
        const formattedDate = new Date(post.date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        article.innerHTML = `
            <div class="aspect-w-16 aspect-h-9 mb-6">
                <img src="${post.image}" alt="${post.title}" 
                     class="w-full h-48 object-cover rounded-lg"
                     onerror="this.src='assets/images/placeholder.jpg'">
            </div>
            
            <div class="flex items-center text-sm text-gray-500 mb-3">
                <span class="category-tag">${this.getCategoryName(post.category)}</span>
                <span class="mx-2">•</span>
                <time datetime="${post.date}">${formattedDate}</time>
                <span class="mx-2">•</span>
                <span>${post.readTime}</span>
            </div>
            
            <h2 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                <a href="posts/2025/01/${post.slug}/" class="hover:text-blue-600 transition-colors">
                    ${post.title}
                </a>
            </h2>
            
            <p class="text-gray-600 mb-4 line-clamp-3">${post.excerpt}</p>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-green-500 flex items-center justify-center text-white text-sm font-bold">
                        ${post.author.charAt(0)}
                    </div>
                    <span class="ml-2 text-sm text-gray-600">${post.author}</span>
                </div>
                
                <a href="posts/2025/01/${post.slug}/" class="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    Read More →
                </a>
            </div>
        `;

        return article;
    }

    getCategoryName(category) {
        const categoryNames = {
            'ai-writing': 'AI Writing',
            'productivity': 'Productivity',
            'tutorials': 'Tutorials',
            'features': 'Features',
            'case-studies': 'Case Studies'
        };
        return categoryNames[category] || category;
    }
}

// Initialize blog when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BlogManager();
});

// Navbar scroll effect
window.addEventListener('scroll', function () {
    const nav = document.querySelector('nav');
    if (window.scrollY > 100) {
        nav.classList.add('nav-scrolled');
    } else {
        nav.classList.remove('nav-scrolled');
    }
});
