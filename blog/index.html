<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Blog - DocForge AI | AI-Powered Document Creation Insights</title>
    <meta name="description"
        content="Discover tips, tutorials, and insights about AI-powered document creation. Learn how to create amazing documents 10x faster with DocForge AI." />
    <link rel="stylesheet" href="../css/styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Blog - DocForge AI" />
    <meta property="og:description" content="Discover tips, tutorials, and insights about AI-powered document creation." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://docforgeai.com/blog/" />
    <meta property="og:image" content="https://docforgeai.com/blog/assets/images/blog-og-image.jpg" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Blog - DocForge AI" />
    <meta name="twitter:description" content="Discover tips, tutorials, and insights about AI-powered document creation." />
    <meta name="twitter:image" content="https://docforgeai.com/blog/assets/images/blog-og-image.jpg" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": "DocForge AI Blog",
        "description": "Tips, tutorials, and insights about AI-powered document creation",
        "url": "https://docforgeai.com/blog/",
        "publisher": {
            "@type": "Organization",
            "name": "DocForge AI",
            "logo": {
                "@type": "ImageObject",
                "url": "https://docforgeai.com/public/favicon.ico"
            }
        }
    }
    </script>
</head>

<body class="bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 nav-glass z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <a href="../index.html" class="text-2xl font-bold text-gray-900">DocForge AI</a>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="../index.html#features" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;"
                        onmouseover="this.style.color='#1E3A8A'" onmouseout="this.style.color='#6B7280'">Features</a>
                    <a href="../index.html#pricing" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;"
                        onmouseover="this.style.color='#1E3A8A'" onmouseout="this.style.color='#6B7280'">Pricing</a>
                    <a href="index.html" class="text-blue-600 transition-colors font-medium"
                        style="color: #1E3A8A;">Blog</a>
                    <a href="../contact-us/index.html" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Contact</a>
                    <a href="https://docforgeai.netlify.app/" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Sign In</a>
                    <a href="https://docforgeai.netlify.app/" class="btn-primary px-4 py-2 text-sm">Start Free Trial</a>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-4 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                <a href="../index.html#features"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Features</a>
                <a href="../index.html#pricing"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Pricing</a>
                <a href="index.html"
                    class="block px-3 py-2 text-blue-600 bg-blue-50 rounded-lg transition-colors">Blog</a>
                <a href="../contact-us/index.html"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Contact</a>
                <a href="https://docforgeai.netlify.app/"
                    class="block w-full text-left px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Sign
                    In</a>
                <a href="https://docforgeai.netlify.app/" class="btn-primary w-full mt-4 px-4 py-2 text-sm">Start Free
                    Trial</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Blog Header -->
        <section class="pt-32 pb-16 bg-mesh relative overflow-hidden">
            <div class="pattern-dots absolute inset-0 opacity-30"></div>
            
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">
                    <h1 class="text-5xl md:text-6xl font-black text-gray-900 mb-6 leading-tight">
                        DocForge AI
                        <span class="text-gradient block mt-2">Blog</span>
                    </h1>
                    <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                        Discover tips, tutorials, and insights to create amazing documents with AI. 
                        Learn from experts and boost your productivity.
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="max-w-md mx-auto">
                        <div class="relative">
                            <input type="text" id="blog-search" placeholder="Search articles..." 
                                class="w-full px-4 py-3 pl-12 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all">
                            <svg class="w-5 h-5 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blog Categories -->
        <section class="py-8 bg-white border-b border-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="#" class="category-filter active px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="all">
                        All Posts
                    </a>
                    <a href="#" class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="ai-writing">
                        AI Writing
                    </a>
                    <a href="#" class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="productivity">
                        Productivity
                    </a>
                    <a href="#" class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="tutorials">
                        Tutorials
                    </a>
                    <a href="#" class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="features">
                        Features
                    </a>
                    <a href="#" class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-all" data-category="case-studies">
                        Case Studies
                    </a>
                </div>
            </div>
        </section>

        <!-- Blog Posts Grid -->
        <section class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div id="blog-posts-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Blog posts will be loaded here -->
                </div>
                
                <!-- Load More Button -->
                <div class="text-center mt-12">
                    <button id="load-more-btn" class="btn-secondary px-6 py-3">
                        Load More Articles
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center mb-6">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <span class="text-2xl font-bold">DocForge AI</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Transform ideas into professional documents 10x faster with our AI-powered document creation
                        platform.
                    </p>
                </div>

                <!-- Product Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="../index.html#features" class="text-gray-300 hover:text-white transition-all duration-200">Features</a></li>
                        <li><a href="../index.html#pricing" class="text-gray-300 hover:text-white transition-all duration-200">Pricing</a></li>
                        <li><a href="index.html" class="text-gray-300 hover:text-white transition-all duration-200">Blog</a></li>
                        <li><a href="https://docforgeai.netlify.app/" class="text-gray-300 hover:text-white transition-all duration-200">Try Free</a></li>
                    </ul>
                </div>

                <!-- Support Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="../contact-us/index.html" class="text-gray-300 hover:text-white transition-all duration-200">Contact Us</a></li>
                        <li><a href="../privacy-policy/index.html" class="text-gray-300 hover:text-white transition-all duration-200">Privacy Policy</a></li>
                        <li><a href="../terms-of-use/index.html" class="text-gray-300 hover:text-white transition-all duration-200">Terms of Use</a></li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-800 mt-12 py-6 text-center text-gray-300 text-sm">
                <p>&copy; 2025 DocForge AI. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/blog.js"></script>
</body>

</html>
