<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DocForge AI - Create Amazing Documents with AI Magic</title>
    <meta name="description"
        content="Transform ideas into professional documents 10x faster with DocForge AI. Advanced AI-powered document creation for professionals, students, and researchers." />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

</head>

<body class="bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 nav-glass z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <span class="text-2xl font-bold text-gray-900">DocForge AI</span>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;"
                        onmouseover="this.style.color='#1E3A8A'" onmouseout="this.style.color='#6B7280'">Features</a>
                    <a href="#pricing" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;"
                        onmouseover="this.style.color='#1E3A8A'" onmouseout="this.style.color='#6B7280'">Pricing</a>
                    <a href="blog/" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Blog</a>
                    <a href="contact-us/index.html" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Contact</a>
                    <a href="https://docforgeai.netlify.app/" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Sign In</a>
                    <a href="https://docforgeai.netlify.app/" class="btn-primary px-4 py-2 text-sm">Start Free Trial</a>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-4 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                <a href="#features"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Features</a>
                <a href="#pricing"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Pricing</a>
                <a href="blog/"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Blog</a>
                <a href="contact-us/index.html"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Contact</a>
                <a href="https://docforgeai.netlify.app/"
                    class="block w-full text-left px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Sign
                    In</a>
                <a href="https://docforgeai.netlify.app/" class="btn-primary w-full mt-4 px-4 py-2 text-sm">Start Free
                    Trial</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="pt-32 pb-20 bg-mesh relative overflow-hidden">
            <div class="pattern-dots absolute inset-0 opacity-30"></div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">
                    <!-- Live Counter -->
                    <div
                        class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-xl border border-white/20 rounded-full text-sm font-medium mb-8 shadow-lg">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                        <span class="font-bold text-green-600" id="live-counter">2,847</span>
                        <span class="text-gray-600 ml-1">documents created today</span>
                    </div>

                    <!-- Main Heading -->
                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
                        Create Amazing Documents with
                        <span class="text-gradient block mt-2">AI Magic</span>
                    </h1>

                    <!-- Subheading -->
                    <p class="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                        Transform ideas into professional documents <strong style="color: #10B981;"
                            class="font-bold">10x faster</strong> than traditional tools.
                        Our AI understands context, generates intelligent content, and provides enterprise-grade editing
                        capabilities.
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                        <a href="https://docforgeai.netlify.app/" class="btn-primary text-lg px-6 py-3 group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Start Free Trial
                        </a>
                        <a class="btn-secondary text-lg px-6 py-3 group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                    clip-rule="evenodd" />
                            </svg>
                            Watch Demo
                        </a>
                    </div>

                    <!-- Trust Indicators -->
                    <div class="text-sm text-gray-500 flex flex-wrap justify-center gap-8">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span>No credit card required</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span>14-day free trial</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span>Cancel anytime</span>
                        </div>
                    </div>
                </div>

                <!-- Hero Visual -->
                <div class="mt-20 max-w-5xl mx-auto">
                    <div class="glass-card p-8 hover-lift">
                        <!-- Document Interface Preview -->
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                            <div class="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full font-medium">DocForge
                                AI</div>
                        </div>

                        <div class="space-y-4">
                            <div class="h-4 bg-gradient-muted rounded animate-pulse"></div>
                            <div class="h-4 bg-gradient-muted rounded w-3/4 animate-pulse"></div>
                            <div class="h-4 bg-gradient-muted rounded w-1/2 animate-pulse"></div>

                            <div class="flex items-center p-4 rounded-lg mt-6" style="background-color: #EBF8FF;">
                                <svg class="w-5 h-5 mr-3 animate-spin" fill="currentColor" viewBox="0 0 20 20"
                                    style="color: #1E3A8A;">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                <span class="text-sm font-semibold" style="color: #1E3A8A;">AI is enhancing your
                                    content...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-16 bg-white border-b border-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                    <div class="fade-in">
                        <div class="text-4xl md:text-5xl font-black text-gray-900 mb-2" id="docs-counter">50,000+</div>
                        <div class="text-gray-600 font-medium">Documents Created</div>
                    </div>
                    <div class="fade-in">
                        <div class="text-4xl md:text-5xl font-black text-gray-900 mb-2" id="users-counter">10,000+</div>
                        <div class="text-gray-600 font-medium">Happy Users</div>
                    </div>
                    <div class="fade-in">
                        <div class="text-4xl md:text-5xl font-black text-gray-900 mb-2">99%</div>
                        <div class="text-gray-600 font-medium">Satisfaction Rate</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-black text-gray-900 mb-6">
                        Powerful Features for
                        <span class="text-gradient">Every Need</span>
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        From AI-powered writing assistance to advanced analytics tools, DocForge AI has everything you
                        need to create amazing documents.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Feature 1 -->
                    <div class="card hover-lift fade-in">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 icon-bg-blue">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">AI-Powered Writing</h3>
                        <p class="text-gray-600">Generate high-quality content with our advanced AI that understands
                            context and maintains your unique voice.</p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="card hover-lift fade-in">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 icon-bg-teal">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Smart Templates</h3>
                        <p class="text-gray-600">Choose from hundreds of professionally designed templates that adapt to
                            your content and style preferences.</p>
                    </div>

                    <!-- Feature 4 -->
                    <div class="card hover-lift fade-in">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 icon-bg-orange">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Advanced Analytics</h3>
                        <p class="text-gray-600">Track document performance, readability scores, and engagement metrics
                            to optimize your content.</p>
                    </div>

                    <!-- Feature 5 -->
                    <div class="card hover-lift fade-in">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 icon-bg-green">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Enterprise Security</h3>
                        <p class="text-gray-600">Bank-level encryption, SSO integration, and compliance with SOC 2,
                            GDPR, and HIPAA standards.</p>
                    </div>

                    <!-- Feature 6 -->
                    <div class="card hover-lift fade-in">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 icon-bg-blue">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Export Anywhere</h3>
                        <p class="text-gray-600">Export to PDF, Word, Google Docs, or publish directly to your website
                            with one click.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pricing Section -->
        <section id="pricing" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-black text-gray-900 mb-6">
                        Simple, Transparent
                        <span class="text-gradient">Pricing</span>
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Choose the perfect plan for your needs. All plans include our core AI features and 24/7 support.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                    <!-- Basic Plan -->
                    <div class="card text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Basic</h3>
                        <div class="text-4xl font-black text-gray-900 mb-2">$9</div>
                        <div class="text-gray-500 mb-8">per month</div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                25 documents per month
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Basic AI writing assistance
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                50+ templates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Email support
                            </li>
                        </ul>
                        <a href="https://docforgeai.netlify.app/" class="btn-secondary w-full">Get Started</a>
                    </div>

                    <!-- Standard Plan -->
                    <div class="card text-center pricing-card" style="border: 2px solid #1E3A8A;">
                        <div class="badge">Most Popular</div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Standard</h3>
                        <div class="text-4xl font-black text-gray-900 mb-2">$29</div>
                        <div class="text-gray-500 mb-8">per month</div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Unlimited documents
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Advanced AI features
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                200+ premium templates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Priority support
                            </li>
                        </ul>
                        <a href="https://docforgeai.netlify.app/" class="btn-primary w-full px-4 py-2 text-sm">Start
                            Free Trial</a>
                    </div>

                    <!-- Pro Plan -->
                    <div class="card text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Pro</h3>
                        <div class="text-4xl font-black text-gray-900 mb-2">$79</div>
                        <div class="text-gray-500 mb-8">per month</div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Everything in Standard
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Advanced analytics & insights
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Team collaboration tools
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                API access & integrations
                            </li>
                        </ul>
                        <a href="https://docforgeai.netlify.app/" class="btn-secondary w-full">Contact Sales</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-black text-gray-900 mb-6">
                        Loved by
                        <span class="text-gradient">Thousands</span>
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        See what our users are saying about DocForge AI and how it's transforming their document
                        creation process.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Testimonial 1 -->
                    <div class="card hover-lift">
                        <div class="flex items-center mb-4">
                            <div
                                class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold icon-bg-blue">
                                S
                            </div>
                            <div class="ml-4">
                                <div class="font-semibold text-gray-900">Sarah Chen</div>
                                <div class="text-sm text-gray-500">Marketing Director</div>
                            </div>
                        </div>
                        <p class="text-gray-600 italic">
                            "DocForge AI has completely transformed how I create content. What used to take hours now
                            takes minutes, and the quality is consistently excellent."
                        </p>
                        <div class="flex text-yellow-400 mt-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="card hover-lift">
                        <div class="flex items-center mb-4">
                            <div
                                class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold icon-bg-teal">
                                M
                            </div>
                            <div class="ml-4">
                                <div class="font-semibold text-gray-900">Michael Rodriguez</div>
                                <div class="text-sm text-gray-500">Freelance Writer</div>
                            </div>
                        </div>
                        <p class="text-gray-600 italic">
                            "As a freelancer, efficiency is everything. DocForge AI helps me deliver high-quality work
                            to my clients faster than ever before. It's like having a writing assistant that never
                            sleeps."
                        </p>
                        <div class="flex text-yellow-400 mt-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="card hover-lift">
                        <div class="flex items-center mb-4">
                            <div
                                class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold icon-bg-purple">
                                A
                            </div>
                            <div class="ml-4">
                                <div class="font-semibold text-gray-900">Amanda Taylor</div>
                                <div class="text-sm text-gray-500">PhD Student</div>
                            </div>
                        </div>
                        <p class="text-gray-600 italic">
                            "Writing my dissertation became so much easier with DocForge AI. The research assistance and
                            citation tools are incredible. I wish I had this during my entire PhD journey!"
                        </p>
                        <div class="flex text-yellow-400 mt-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 text-white section-bg-primary">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-4xl md:text-5xl font-black mb-6 text-white">
                    Ready to Transform Your Documents?
                </h2>
                <p class="text-xl mb-10 text-white">
                    Join thousands of professionals who are already creating amazing documents 10x faster with DocForge
                    AI.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="https://docforgeai.netlify.app/"
                        class="bg-white text-lg shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 px-4 py-2 rounded-xl font-bold"
                        style="color: #1E3A8A;" onmouseover="this.style.backgroundColor='#F3F4F6'"
                        onmouseout="this.style.backgroundColor='#FFFFFF'">
                        Start Your Free Trial
                    </a>
                    <a class="border-2 border-white text-white px-4 py-2 rounded-xl font-bold text-lg transform hover:-translate-y-1 transition-all duration-300"
                        onmouseover="this.style.backgroundColor='#FFFFFF'; this.style.color='#1E3A8A'"
                        onmouseout="this.style.backgroundColor='transparent'; this.style.color='#FFFFFF'">
                        Schedule Demo
                    </a>
                </div>
                <p class="text-sm mt-6 text-white">
                    • No credit card required • 14-day free trial • Cancel anytime
                </p>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center mb-6">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <span class="text-2xl font-bold">DocForge AI</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Transform ideas into professional documents 10x faster with our AI-powered document creation
                        platform.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-white transition-all duration-200"
                            onmouseover="this.style.textDecoration='underline'"
                            onmouseout="this.style.textDecoration='none'" aria-label="Follow us on Twitter">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                            </svg>
                        </a>
                        <a href="#" class="text-white transition-all duration-200"
                            onmouseover="this.style.textDecoration='underline'"
                            onmouseout="this.style.textDecoration='none'" aria-label="Connect with us on LinkedIn">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Product Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Features</a></li>
                        <li><a href="#pricing" class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Pricing</a></li>
                        <li><a href="#testimonials" class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Testimonials</a></li>
                        <li><a href="https://docforgeai.netlify.app/"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Try Free</a></li>
                    </ul>
                </div>

                <!-- Support Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="contact-us/index.html"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Contact Us</a></li>
                        <li><a href="privacy-policy/index.html"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Privacy Policy</a></li>
                        <li><a href="terms-of-use/index.html"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Terms of Use</a></li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-800 mt-12 py-6 text-center text-gray-300 text-sm">
                <p>&copy; 2025 DocForge AI. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function () {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function (entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all elements with fade-in class
        document.querySelectorAll('.fade-in').forEach(element => {
            observer.observe(element);
        });

        // Live counter animation
        let liveCounter = document.getElementById('live-counter');
        setInterval(() => {
            const currentValue = parseInt(liveCounter.textContent.replace(/,/g, ''));
            const increment = Math.floor(Math.random() * 5) + 1;
            liveCounter.textContent = (currentValue + increment).toLocaleString();
        }, 3000);

        // Navbar background on scroll
        window.addEventListener('scroll', function () {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.98)';
                nav.classList.add('shadow-lg');
            } else {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.classList.remove('shadow-lg');
            }
        });
    </script>

</body>

</html>