// Service Worker for Blog Performance Optimization
const CACHE_NAME = "docforge-blog-v1";
const STATIC_CACHE_NAME = "docforge-static-v1";

// Resources to cache immediately
const STATIC_RESOURCES = ["/css/styles.css", "/js/main.js"];

// Install event - cache static resources
self.addEventListener("install", (event) => {
  console.log("Service Worker installing...");

  event.waitUntil(
    caches
      .open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log("Caching static resources");
        return cache.addAll(STATIC_RESOURCES);
      })
      .then(() => {
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error("Failed to cache static resources:", error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  console.log("Service Worker activating...");

  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
              console.log("Deleting old cache:", cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener("fetch", (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Only handle GET requests
  if (request.method !== "GET") {
    return;
  }
  if (
    url.pathname.startsWith("/css/") ||
    url.pathname.startsWith("/js/")
  ) {
    event.respondWith(
      caches.match(request).then((cachedResponse) => {
        if (cachedResponse) {
          // Serve from cache
          return cachedResponse;
        }

        // Fetch from network and cache
        return fetch(request)
          .then((response) => {
            // Don't cache non-successful responses
            if (
              !response ||
              response.status !== 200 ||
              response.type !== "basic"
            ) {
              return response;
            }

            // Clone the response for caching
            const responseToCache = response.clone();

            // Determine cache name based on resource type
            const cacheName = isStaticResource(url.pathname)
              ? STATIC_CACHE_NAME
              : CACHE_NAME;

            caches
              .open(cacheName)
              .then((cache) => {
                cache.put(request, responseToCache);
              })
              .catch((error) => {
                console.warn("Failed to cache resource:", error);
              });

            return response;
          })
          .catch((error) => {
            console.error("Network request failed:", error);

            // Return a fallback response for critical resources
            if (url.pathname.endsWith(".json")) {
              return new Response(
                JSON.stringify({ posts: [], categories: [], settings: {} }),
                { headers: { "Content-Type": "application/json" } }
              );
            }

            throw error;
          });
      })
    );
  }
});

// Helper function to determine if a resource is static
function isStaticResource(pathname) {
  return (
    pathname.endsWith(".css") ||
    pathname.endsWith(".js") ||
    pathname.endsWith(".json") ||
    pathname.includes("/assets/")
  );
}


// Handle push notifications (future enhancement)
self.addEventListener("push", (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: "/public/favicon.ico",
      badge: "/public/favicon.ico",
    };

    event.waitUntil(self.registration.showNotification(data.title, options));
  }
});
